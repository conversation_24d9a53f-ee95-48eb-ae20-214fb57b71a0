"""
Configuration builder functions for DataYatesV1 models.

This module provides functions for building configuration dictionaries for
different model components. These functions convert simple parameter dictionaries
into the more complex nested structure expected by the modular system.
"""

from typing import Dict, Any, Optional, Union, Tuple, List

def build_convblock_config(
    in_channels: int,
    out_channels: int,
    kernel_size: Union[int, Tuple[int, ...]] = (3, 3, 3),
    stride: Union[int, Tuple[int, ...]] = 1,
    padding: Union[int, Tuple[int, ...]] = 1,
    dim: int = 3,
    conv_type: str = 'standard',
    norm_type: str = 'batch',
    act_type: str = 'relu',
    use_layernorm: bool = False,
    causal: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Build a configuration dictionary for a ConvBlock.

    Args:
        in_channels: Number of input channels
        out_channels: Number of output channels
        kernel_size: Kernel size for convolution
        stride: Stride for convolution
        padding: Padding for convolution
        dim: Dimensionality of convolution (2 or 3)
        conv_type: Type of convolution ('standard', 'stacked2d', etc.)
        norm_type: Type of normalization ('batch', 'layer', etc.)
        act_type: Type of activation ('relu', 'leakyrelu', etc.)
        use_layernorm: Whether to use layer normalization (overrides norm_type)
        causal: Whether to use causal padding for 3D convolutions
        **kwargs: Additional parameters to include in the configuration

    Returns:
        Dictionary containing the configuration for a ConvBlock
    """
    # Override norm_type if use_layernorm is specified
    if use_layernorm:
        norm_type = 'layer'

    # Build the configuration dictionary
    config = {
        'in_channels': in_channels,
        'out_channels': out_channels,
        'dim': dim,
        'conv_params': {
            'type': conv_type,
            'kernel_size': kernel_size,
            'stride': stride,
            'padding': padding
        },
        'norm_type': norm_type,
        'act_type': act_type,
        'causal': causal
    }

    # Add any additional parameters
    for key, value in kwargs.items():
        if key == 'conv_params' and isinstance(value, dict):
            # Merge nested conv_params dictionary
            config['conv_params'].update(value)
        elif key == 'norm_params' and isinstance(value, dict):
            # Add norm_params dictionary
            config['norm_params'] = value
        elif key == 'act_params' and isinstance(value, dict):
            # Add act_params dictionary
            config['act_params'] = value
        elif key == 'pool_params' and isinstance(value, dict):
            # Add pool_params dictionary
            config['pool_params'] = value
        else:
            # Regular parameter
            config[key] = value

    return config

def build_densenet_config(
    initial_channels: int,
    growth_rate: int = 4,
    num_blocks: int = 3,
    kernel_size: Union[int, Tuple[int, ...]] = (3, 3, 3),
    dim: int = 3,
    use_layernorm: bool = False,
    use_checkpointing: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Build a configuration dictionary for a DenseNet.

    Args:
        initial_channels: Number of input channels
        growth_rate: Growth rate for DenseNet
        num_blocks: Number of dense blocks
        kernel_size: Kernel size for convolutions
        dim: Dimensionality of convolution (2 or 3)
        use_layernorm: Whether to use layer normalization
        use_checkpointing: Whether to use checkpointing
        **kwargs: Additional parameters to include in the configuration

    Returns:
        Dictionary containing the configuration for a DenseNet
    """
    # Build the configuration dictionary
    config = {
        'model_type': 'densenet',
        'dim': dim,
        'initial_channels': initial_channels,
        'growth_rate': growth_rate,
        'num_blocks': num_blocks,
        'checkpointing': use_checkpointing,
        'block_config': {
            'conv_params': {
                'type': 'standard',
                'kernel_size': kernel_size,
                'padding': 1
            },
            'norm_type': 'layer' if use_layernorm else 'batch',
            'act_type': 'relu'
        }
    }

    # Add any additional parameters
    for key, value in kwargs.items():
        if key == 'block_config' and isinstance(value, dict):
            # Merge nested block_config dictionary
            config['block_config'].update(value)
        else:
            # Regular parameter
            config[key] = value

    return config

def build_resnet_config(
    in_channels: int,
    out_channels: int = 32,
    kernel_size: Union[int, Tuple[int, ...]] = (3, 3, 3),
    stride: Union[int, Tuple[int, ...]] = 1,
    dim: int = 3,
    use_layernorm: bool = False,
    use_checkpointing: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Build a configuration dictionary for a ResNet.

    Args:
        in_channels: Number of input channels
        out_channels: Number of output channels
        kernel_size: Kernel size for convolutions
        stride: Stride for convolutions
        dim: Dimensionality of convolution (2 or 3)
        use_layernorm: Whether to use layer normalization
        use_checkpointing: Whether to use checkpointing
        **kwargs: Additional parameters to include in the configuration

    Returns:
        Dictionary containing the configuration for a ResNet
    """
    # Determine normalization type
    norm_type = 'layer' if use_layernorm else 'batch'

    # Build the configuration dictionary
    config = {
        'model_type': 'resnet',
        'dim': dim,
        'initial_channels': in_channels,
        'base_channels': out_channels,
        'checkpointing': use_checkpointing,
        'resnet_shortcut_norm_type': norm_type,
        'resnet_post_add_activation': 'relu',
        'stem_config': {
            'out_channels': out_channels,
            'conv_params': {
                'type': 'standard',
                'kernel_size': 3,
                'stride': 1,
                'padding': 1
            },
            'norm_type': norm_type,
            'act_type': 'relu'
        },
        'layer_configs': [
            {
                'channel_multiplier': 1,
                'conv_params': {
                    'type': 'standard',
                    'kernel_size': kernel_size,
                    'stride': stride,
                    'padding': 1
                },
                'norm_type': norm_type,
                'act_type': 'relu'
            }
        ]
    }

    # Add any additional parameters
    for key, value in kwargs.items():
        if key == 'stem_config' and isinstance(value, dict):
            # Merge nested stem_config dictionary
            config['stem_config'].update(value)
        elif key == 'layer_configs' and isinstance(value, list):
            # Replace layer_configs list
            config['layer_configs'] = value
        else:
            # Regular parameter
            config[key] = value

    return config

def build_model_config(
    model_type: str = 'v1',
    frontend_type: str = 'da',
    convnet_type: str = 'densenet',
    recurrent_type: str = 'none',
    modulator_type: str = 'none',
    readout_type: str = 'gaussian',
    frontend_params: Optional[Dict[str, Any]] = None,
    convnet_params: Optional[Dict[str, Any]] = None,
    recurrent_params: Optional[Dict[str, Any]] = None,
    modulator_params: Optional[Dict[str, Any]] = None,
    readout_params: Optional[Dict[str, Any]] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Build a complete model configuration dictionary.

    Args:
        model_type: Type of model to build ('v1')
        frontend_type: Type of frontend ('da', 'conv', 'none')
        convnet_type: Type of convnet ('densenet', 'conv', 'none')
        recurrent_type: Type of recurrent layer ('convlstm', 'convgru', 'none')
        modulator_type: Type of modulator ('lstm', 'linear', 'none')
        readout_type: Type of readout ('gaussian', 'linear')
        frontend_params: Parameters for frontend
        convnet_params: Parameters for convnet
        recurrent_params: Parameters for recurrent layer
        modulator_params: Parameters for modulator
        readout_params: Parameters for readout
        **kwargs: Additional parameters to include in the configuration

    Returns:
        Dictionary containing the complete model configuration
    """
    # Initialize parameter dictionaries if not provided
    frontend_params = frontend_params or {}
    convnet_params = convnet_params or {}
    recurrent_params = recurrent_params or {}
    modulator_params = modulator_params or {}
    readout_params = readout_params or {}

    # Build the configuration dictionary
    config = {
        'model_type': model_type,
        'frontend_type': frontend_type,
        'convnet_type': convnet_type,
        'recurrent_type': recurrent_type,
        'modulator_type': modulator_type,
        'readout_type': readout_type,
        'frontend_params': frontend_params,
        'convnet_params': convnet_params,
        'recurrent_params': recurrent_params,
        'modulator_params': modulator_params,
        'readout_params': readout_params
    }

    # Add any additional parameters
    for key, value in kwargs.items():
        config[key] = value

    return config
